<?php

declare(strict_types=1);

namespace App\Livewire;

use Livewire\Component;

class Verse extends Component
{
    public $verse;

    public $selected = false;

    public $selectionMode = false;

    public function mount($verse, $selectionMode = false): void
    {
        $this->verse = $verse;
        $this->selectionMode = $selectionMode;
    }

    public function selectVerse(): void
    {
        if ($this->selectionMode) {
            // In selection mode, dispatch event to parent component
            $this->dispatch('verse-selected', verseId: $this->verse->id);
        } else {
            // Normal toggle behavior
            if ($this->selected) {
                $this->selected = false;
            } else {
                $this->selected = true;
            }
        }
    }

    public function render()
    {
        return view('livewire.verse');
    }
}
