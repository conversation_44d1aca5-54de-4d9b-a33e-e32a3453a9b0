<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\Chapter;
use App\Models\Verse;
use Illuminate\View\View;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\Component;

class VerseSelector extends Component
{
    public int $loaded = 30;

    #[Url]
    public $selectedChapter = null;

    public $chapters;

    #[Url]
    public $search = '';

    public $fieldName;

    public function mount($fieldName = null): void
    {
        $this->chapters = Chapter::all()->sortBy('number');
        $this->fieldName = $fieldName;
    }

    public function loadMore(): void
    {
        $this->loaded += 30;
    }

    #[On('verse-selected')]
    public function handleVerseSelection($verseId): void
    {
        dd($verseId);
        // Dispatch event to parent component (the form) with the selected verse ID
        $this->dispatch('verse-selected-for-field', [
            'fieldName' => $this->fieldName,
            'verseId' => $verseId
        ])->to('app.filament.resources.question-resource.pages.create-question');

        // Add a JavaScript snippet to close the modal
        $this->js('
            // Find and close the modal
            const modal = document.querySelector("[x-data*=\'modal\']");
            if (modal && modal.__x) {
                modal.__x.$data.isOpen = false;
            }
            // Alternative approach - trigger ESC key
            document.dispatchEvent(new KeyboardEvent("keydown", { key: "Escape" }));
        ');
    }

    public function render(): View
    {
        $query = Verse::with('chapter');

        if ($this->search) {
            $query->whereTextStartsWith($this->search);
        }

        if ($this->selectedChapter) {
            $query->where('chapter_id', $this->selectedChapter);
        }

        // Join with chapters table to order by chapters.number
        $verses = $query->join('chapters', 'verses.chapter_id', '=', 'chapters.id')
            ->orderBy('chapters.number')
            ->orderBy('verses.number')
            ->select('verses.*')
            ->take($this->loaded)
            ->get();

        // Group by chapter_id and page
        $grouped = $verses->groupBy(function ($verse) {
            return $verse->chapter_id.'{-}'.$verse->page;
        });

        return view('livewire.verse-selector', [
            'grouped' => $grouped,
            'chapters' => $this->chapters,
        ]);
    }
}
