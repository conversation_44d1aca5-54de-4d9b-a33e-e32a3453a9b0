<?php

declare(strict_types=1);

namespace App\Filament\Resources\QuestionResource\Pages;

use App\Filament\Resources\QuestionResource;
use App\Models\Verse;
use Exception;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\CreateRecord;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Utilities\Get;
use Filament\Schemas\Schema;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\On;

class CreateQuestion extends CreateRecord
{
    public string $startVerseId = '';

    public string $endVerseId = '';

    protected static string $resource = QuestionResource::class;

    #[On('verse-selected-for-field')]
    public function handleVerseSelection($data): void
    {

        $fieldName = $data['fieldName'];
        $verseId = $data['verseId'];

        if ($fieldName === 'start_verse_id') {
            $this->startVerseId = $verseId;
        } elseif ($fieldName === 'end_verse_id') {
            $this->endVerseId = $verseId;
        }
    }

    /**
     * @throws Exception
     */
    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('teacher_id')
                    ->label('Teacher')
                    ->translateLabel()
                    ->relationship('teacher', 'name')
                    ->preload()
                    ->searchable()
                    ->required(),

                // select the eighth system or start_end system
                Select::make('system')
                    ->label('System')
                    ->translateLabel()
                    ->live()
                    ->default('start_end')
                    ->options([
                        'eighth' => __('Eighth'),
                        'start_end' => __('Start End'),
                    ])
                    ->required(),

                Section::make(__('Verses'))
                    ->visible(function (Get $get): bool {
                        return $get('system') === 'eighth' || $get('system') === 'start_end';
                    })
                    ->schema([
                        Select::make('verse_id')
                            ->label('Verse')
                            ->translateLabel()
                            ->options(
                                Verse::where('eighth', 1)->pluck('text', 'id')
                            )
                            ->preload()
                            ->searchable()
                            ->visible(function (Get $get): bool {
                                return $get('system') === 'eighth';
                            })
                            ->required(),

                        Select::make('start_verse_id')
                            ->label('Start Verse')
                            ->translateLabel()
                            ->relationship('startVerse', 'text')
                            ->searchable()
                            ->suffixAction(
                                Action::make('selectStartVerse')
                                    ->icon('heroicon-m-magnifying-glass')
                                    ->modalHeading('Select Start Verse')
                                    ->modalWidth('4xl')
                                    ->slideOver()
                                    ->closeModalByClickingAway()
                                    ->closeModalByEscaping()
                                    ->modalSubmitActionLabel(__('Select Verse'))
                                    ->action(function () {
                                        $this->form->fill([
                                            'system' => 'start_end',
                                            'start_verse_id' => $this->startVerseId,
                                            'end_verse_id' => $this->endVerseId,
                                        ]);
                                    })
                                    ->modalContent(fn () => new HtmlString(
                                        Blade::render('@livewire("verse-selector", ["fieldName" => "start_verse_id"])')
                                    ))
                            )
                            ->visible(function (Get $get): bool {
                                return $get('system') === 'start_end';
                            })
                            ->required(),

                        Select::make('end_verse_id')
                            ->label('End Verse')
                            ->translateLabel()
                            ->relationship('endVerse', 'text')
                            ->searchable()
                            ->extraAttributes([
                                'font-family' => 'UthmanicQaloun',
                            ])
                            ->suffixAction(
                                Action::make('selectEndVerse')
                                    ->icon('heroicon-m-magnifying-glass')
                                    ->modalHeading('Select End Verse')
                                    ->modalWidth('4xl')
                                    ->slideOver()
                                    ->closeModalByClickingAway()
                                    ->closeModalByEscaping()
                                    ->modalSubmitActionLabel(__('Select Verse'))
                                    ->action(function () {
                                        $this->form->fill([
                                            'system' => 'start_end',
                                            'start_verse_id' => $this->startVerseId,
                                            'end_verse_id' => $this->endVerseId,
                                        ]);
                                    })
                                    ->modalContent(fn () => new HtmlString(
                                        Blade::render('@livewire("verse-selector", ["fieldName" => "end_verse_id"])')
                                    ))
                            )
                            ->visible(function (Get $get): bool {
                                return $get('system') === 'start_end';
                            })
                            ->required(),
                    ])->columns(2),

                Select::make('status')
                    ->label('Status')
                    ->translateLabel()
                    ->options([
                        'draft' => __('Draft'),
                        'published' => __('Published'),
                        'archived' => __('Archived'),
                    ])
                    ->required(),

                Select::make('type')
                    ->label('Type')
                    ->translateLabel()
                    ->options([
                        'global' => __('Global'),
                        'personal' => __('Personal'),
                    ])
                    ->required(),
            ])->columns(1);
    }
}
